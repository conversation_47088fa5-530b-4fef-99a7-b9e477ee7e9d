# QAX Code Autocomplete 测试和验证指南

## 概述

本指南提供了完整的测试方案来验证 QAX Code Autocomplete 功能是否正常工作。

## 🚀 快速验证步骤

### 1. 构建和安装扩展

```bash
# 1. 构建扩展
npm run package

# 2. 在 VS Code 中安装
# 方法一：通过 VS Code 命令面板
# Ctrl+Shift+P -> "Extensions: Install from VSIX" -> 选择生成的 .vsix 文件

# 方法二：通过命令行
code --install-extension cline-*.vsix
```

### 2. 基础功能验证

#### 2.1 检查扩展是否正确加载
1. 打开 VS Code
2. 查看活动栏是否有 Cline 图标
3. 打开 Cline 侧边栏
4. 检查开发者控制台是否有错误信息

#### 2.2 检查配置界面
1. 点击 Cline 侧边栏的设置按钮
2. 查看是否有 "Autocomplete" 标签页
3. 点击 Autocomplete 标签页
4. 验证配置选项是否正确显示：
   - ✅ 启用/禁用开关
   - ✅ 模型选择下拉框
   - ✅ API Base URL 输入框
   - ✅ API Key 输入框
   - ✅ 性能设置滑块

#### 2.3 检查状态栏
1. 查看 VS Code 底部状态栏
2. 应该看到 "$(sparkle) QAX Complete" 图标
3. 点击状态栏图标应该能切换启用/禁用状态

## 🧪 详细功能测试

### 3. Autocomplete 核心功能测试

#### 3.1 创建测试文件
创建以下测试文件来验证不同语言的支持：

**test.ts (TypeScript)**
```typescript
// 测试 TypeScript 自动补全
import * as fs from 'fs'

function calculateSum(a: number, b: number) {
    // 在这里输入代码，测试自动补全
    return 
}

class TestClass {
    private data: string[] = []
    
    public addItem(item: string) {
        // 测试方法内的自动补全
        this.
    }
}
```

**test.py (Python)**
```python
# 测试 Python 自动补全
import os
import json

def process_data(data_list):
    # 在这里测试自动补全
    for item in data_list:
        
        
class DataProcessor:
    def __init__(self):
        self.items = []
    
    def add_item(self, item):
        # 测试类方法中的自动补全
        self.
```

#### 3.2 测试自动补全触发
1. 在测试文件中的空行或不完整语句处输入代码
2. 观察是否出现：
   - ✅ 加载动画（QAX 动画）
   - ✅ 自动补全建议
   - ✅ 状态栏成本更新

#### 3.3 测试缓存功能
1. 在相同上下文中多次触发补全
2. 第二次应该更快（使用缓存）
3. 控制台应该显示 "Using cached completions" 消息

### 4. 配置系统测试

#### 4.1 测试配置保存
1. 修改 Autocomplete 设置
2. 点击 "Save Settings" 按钮
3. 重启 VS Code
4. 验证设置是否保持

#### 4.2 测试配置生效
1. 禁用 autocomplete
2. 验证不再提供补全建议
3. 重新启用
4. 验证补全功能恢复

#### 4.3 测试不同模型
1. 切换到不同的模型
2. 保存设置
3. 测试补全质量是否有差异

## 🔍 调试和诊断

### 5. 日志检查

#### 5.1 VS Code 开发者控制台
```bash
# 打开开发者控制台
Ctrl+Shift+I (Windows/Linux) 或 Cmd+Option+I (Mac)

# 查找相关日志
- "🚀🔍 Autocomplete state changed"
- "🚀🛑 Autocomplete for line with prefix"
- "🚀🎯 Using cached completions"
- "🚀🛑 Bailing out due to too many lines"
```

#### 5.2 扩展输出面板
1. 打开 VS Code 输出面板 (Ctrl+Shift+U)
2. 选择 "Cline" 输出通道
3. 查看扩展相关日志

### 6. 常见问题诊断

#### 6.1 自动补全不工作
**检查清单：**
- [ ] 扩展是否正确安装和激活
- [ ] Autocomplete 是否在设置中启用
- [ ] API 配置是否正确
- [ ] 网络连接是否正常
- [ ] 是否有错误日志

**调试步骤：**
```javascript
// 在开发者控制台中检查配置
console.log(vscode.workspace.getConfiguration('qax-code.autocomplete'))

// 检查实验性功能
console.log(vscode.workspace.getConfiguration('qax-code.experiments'))
```

#### 6.2 状态栏不显示
**可能原因：**
- 扩展未正确激活
- 状态栏被其他扩展覆盖
- 配置错误

#### 6.3 配置界面不显示
**检查：**
- Webview 是否正确加载
- 是否有 JavaScript 错误
- 组件是否正确注册

## 🧪 自动化测试

### 7. 创建单元测试

创建测试文件 `src/test/autocomplete.test.ts`：

```typescript
import * as assert from 'assert'
import * as vscode from 'vscode'
import { ContextGatherer } from '../services/autocomplete/ContextGatherer'
import { processModelResponse } from '../services/autocomplete/AutocompleteProvider'

suite('Autocomplete Tests', () => {
    test('processModelResponse should extract completion', () => {
        const input = '<COMPLETION>console.log("Hello")</COMPLETION>'
        const result = processModelResponse(input)
        assert.strictEqual(result, 'console.log("Hello")')
    })

    test('ContextGatherer should collect context', async () => {
        const gatherer = new ContextGatherer()
        // 创建模拟文档进行测试
        // ... 测试代码
    })
})
```

### 8. 集成测试脚本

创建测试脚本 `scripts/test-autocomplete.js`：

```javascript
const vscode = require('vscode')

async function testAutocomplete() {
    console.log('Testing QAX Code Autocomplete...')
    
    // 1. 检查扩展是否激活
    const extension = vscode.extensions.getExtension('your-publisher.cline')
    if (!extension?.isActive) {
        console.error('❌ Extension not activated')
        return false
    }
    
    // 2. 检查配置
    const config = vscode.workspace.getConfiguration('qax-code.autocomplete')
    console.log('✅ Configuration loaded:', config.get('enabled'))
    
    // 3. 检查命令注册
    const commands = await vscode.commands.getCommands()
    const hasToggleCommand = commands.includes('qax-code.toggleAutocomplete')
    console.log('✅ Toggle command registered:', hasToggleCommand)
    
    return true
}

module.exports = { testAutocomplete }
```

## 📊 性能测试

### 9. 性能基准测试

#### 9.1 响应时间测试
```javascript
// 在开发者控制台中运行
const startTime = performance.now()
// 触发自动补全
// 测量到显示结果的时间
const endTime = performance.now()
console.log(`Autocomplete response time: ${endTime - startTime}ms`)
```

#### 9.2 内存使用测试
1. 打开任务管理器
2. 监控 VS Code 内存使用
3. 大量使用自动补全功能
4. 检查是否有内存泄漏

#### 9.3 缓存效率测试
1. 在相同上下文中重复触发补全
2. 记录缓存命中率
3. 验证缓存是否正确工作

## ✅ 验证清单

### 基础功能
- [ ] 扩展正确安装和激活
- [ ] 配置界面正确显示
- [ ] 状态栏图标显示
- [ ] 可以切换启用/禁用状态

### 核心功能
- [ ] TypeScript 文件中触发自动补全
- [ ] Python 文件中触发自动补全
- [ ] 其他支持语言中触发自动补全
- [ ] 加载动画正确显示
- [ ] 补全建议质量良好

### 配置系统
- [ ] 可以修改和保存设置
- [ ] 设置在重启后保持
- [ ] 不同配置生效
- [ ] 错误配置有适当提示

### 性能和稳定性
- [ ] 响应时间合理（< 2秒）
- [ ] 缓存正确工作
- [ ] 无内存泄漏
- [ ] 无崩溃或错误

## 🎯 用户验收测试

### 最终用户场景测试
1. **新用户首次使用**
   - 安装扩展
   - 打开代码文件
   - 体验自动补全

2. **配置自定义 API**
   - 修改 API 设置
   - 测试不同模型
   - 验证成本跟踪

3. **日常开发工作流**
   - 编写真实代码
   - 使用补全建议
   - 评估提升效果

通过以上完整的测试方案，您可以全面验证 QAX Code Autocomplete 功能是否正常工作！
