import { render, screen } from "@testing-library/react"
import { vi, describe, it, expect, beforeEach } from "vitest"
import AutocompleteSettingsSection from "../AutocompleteSettingsSection"

// Mock vscode module
vi.mock("@/utils/vscode", () => ({
	vscode: {
		postMessage: vi.fn(),
	},
}))

describe("AutocompleteSettingsSection", () => {
	beforeEach(() => {
		vi.clearAllMocks()
	})

	it("should render with default settings", () => {
		render(<AutocompleteSettingsSection />)

		expect(screen.getByText("Enable QAX Code Autocomplete")).toBeInTheDocument()
		expect(screen.getByText("API Configuration")).toBeInTheDocument()
		expect(screen.getByText("Performance Settings")).toBeInTheDocument()
		expect(screen.getByText("Save Settings")).toBeInTheDocument()
	})

	it("should request settings from extension on mount", async () => {
		const { vscode } = await import("@/utils/vscode")
		render(<AutocompleteSettingsSection />)

		expect(vscode.postMessage).toHaveBeenCalledWith({
			type: "getAutocompleteSettings",
		})
	})
})
