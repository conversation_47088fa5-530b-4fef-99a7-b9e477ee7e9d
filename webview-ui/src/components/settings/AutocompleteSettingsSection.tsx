import { VSCodeButton, VSCodeCheckbox, VSCodeTextField, VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"
import { useState, useEffect } from "react"
import { vscode } from "@/utils/vscode"

interface AutocompleteSettings {
	enabled: boolean
	model: string
	apiKey: string
	baseUrl: string
	maxCompletions: number
	debounceMs: number
	maxLines: number
}

const DEFAULT_SETTINGS: AutocompleteSettings = {
	enabled: true,
	model: "Qwen2.5-Coder-32B-Instruct",
	apiKey: "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b",
	baseUrl: "https://aip.b.qianxin-inc.cn/v2",
	maxCompletions: 5,
	debounceMs: 250,
	maxLines: 100,
}

const AVAILABLE_MODELS = [
	"Qwen2.5-Coder-32B-Instruct",
	"Qwen2.5-Coder-14B-Instruct",
	"Qwen2.5-Coder-7B-Instruct",
	"Qwen2.5-Coder-1.5B-Instruct",
	"CodeLlama-34b-Instruct-hf",
	"CodeLlama-13b-Instruct-hf",
	"CodeLlama-7b-Instruct-hf",
]

export default function AutocompleteSettingsSection() {
	const [settings, setSettings] = useState<AutocompleteSettings>(DEFAULT_SETTINGS)
	const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
	const [originalSettings, setOriginalSettings] = useState<AutocompleteSettings>(DEFAULT_SETTINGS)

	// Load settings from VS Code configuration
	useEffect(() => {
		// Request current settings from the extension
		vscode.postMessage({
			type: "getAutocompleteSettings",
		})
	}, [])

	// Listen for settings response from extension
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data
			if (message.type === "autocompleteSettingsResponse" && message.settings) {
				setSettings(message.settings)
				setOriginalSettings(message.settings)
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [])

	// Check for unsaved changes
	useEffect(() => {
		const hasChanges = JSON.stringify(settings) !== JSON.stringify(originalSettings)
		setHasUnsavedChanges(hasChanges)
	}, [settings, originalSettings])

	const handleSave = async () => {
		try {
			// Send message to extension to save settings
			vscode.postMessage({
				type: "autocompleteSettings",
				autocompleteSettings: settings,
			})

			setOriginalSettings(settings)
			setHasUnsavedChanges(false)
		} catch (error) {
			console.error("Failed to save autocomplete settings:", error)
		}
	}

	const handleReset = () => {
		setSettings(originalSettings)
		setHasUnsavedChanges(false)
	}

	const handleResetToDefaults = () => {
		setSettings(DEFAULT_SETTINGS)
	}

	const updateSetting = <K extends keyof AutocompleteSettings>(key: K, value: AutocompleteSettings[K]) => {
		setSettings((prev) => ({ ...prev, [key]: value }))
	}

	return (
		<div className="space-y-4">
			{/* Enable/Disable Toggle */}
			<div className="mb-4">
				<VSCodeCheckbox checked={settings.enabled} onChange={(e: any) => updateSetting("enabled", e.target.checked)}>
					Enable QAX Code Autocomplete
				</VSCodeCheckbox>
				<p className="text-xs mt-2 text-[var(--vscode-descriptionForeground)]">
					Enable or disable the QAX Code autocomplete functionality. When enabled, you'll see intelligent code
					suggestions as you type.
				</p>
			</div>

			{/* API Configuration */}
			<div className="space-y-3">
				<h4 className="text-sm font-medium text-[var(--vscode-foreground)] mb-2">API Configuration</h4>

				<div>
					<label className="block text-xs text-[var(--vscode-descriptionForeground)] mb-1">Model</label>
					<VSCodeDropdown
						value={settings.model}
						onChange={(e: any) => updateSetting("model", e.target.value)}
						className="w-full">
						{AVAILABLE_MODELS.map((model) => (
							<VSCodeOption key={model} value={model}>
								{model}
							</VSCodeOption>
						))}
					</VSCodeDropdown>
					<p className="text-xs mt-1 text-[var(--vscode-descriptionForeground)]">
						Select the AI model to use for code completion
					</p>
				</div>

				<div>
					<label className="block text-xs text-[var(--vscode-descriptionForeground)] mb-1">API Base URL</label>
					<VSCodeTextField
						value={settings.baseUrl}
						onInput={(e: any) => updateSetting("baseUrl", e.target.value)}
						placeholder="https://aip.b.qianxin-inc.cn/v2"
						className="w-full"
					/>
					<p className="text-xs mt-1 text-[var(--vscode-descriptionForeground)]">
						The base URL for the QAX API endpoint
					</p>
				</div>

				<div>
					<label className="block text-xs text-[var(--vscode-descriptionForeground)] mb-1">API Key</label>
					<VSCodeTextField
						value={settings.apiKey}
						onInput={(e: any) => updateSetting("apiKey", e.target.value)}
						type="password"
						placeholder="Enter your API key"
						className="w-full"
					/>
					<p className="text-xs mt-1 text-[var(--vscode-descriptionForeground)]">Your QAX API key for authentication</p>
				</div>
			</div>

			{/* Performance Settings */}
			<div className="space-y-3">
				<h4 className="text-sm font-medium text-[var(--vscode-foreground)] mb-2">Performance Settings</h4>

				<div>
					<label className="block text-xs text-[var(--vscode-descriptionForeground)] mb-1">
						Max Completions ({settings.maxCompletions})
					</label>
					<input
						type="range"
						min="1"
						max="10"
						value={settings.maxCompletions}
						onChange={(e) => updateSetting("maxCompletions", parseInt(e.target.value))}
						className="w-full"
					/>
					<p className="text-xs mt-1 text-[var(--vscode-descriptionForeground)]">
						Maximum number of completion options to cache per context
					</p>
				</div>

				<div>
					<label className="block text-xs text-[var(--vscode-descriptionForeground)] mb-1">
						Debounce Delay ({settings.debounceMs}ms)
					</label>
					<input
						type="range"
						min="100"
						max="1000"
						step="50"
						value={settings.debounceMs}
						onChange={(e) => updateSetting("debounceMs", parseInt(e.target.value))}
						className="w-full"
					/>
					<p className="text-xs mt-1 text-[var(--vscode-descriptionForeground)]">
						Delay before triggering completion requests (lower = more responsive, higher = fewer API calls)
					</p>
				</div>

				<div>
					<label className="block text-xs text-[var(--vscode-descriptionForeground)] mb-1">
						Max Lines ({settings.maxLines})
					</label>
					<input
						type="range"
						min="10"
						max="500"
						step="10"
						value={settings.maxLines}
						onChange={(e) => updateSetting("maxLines", parseInt(e.target.value))}
						className="w-full"
					/>
					<p className="text-xs mt-1 text-[var(--vscode-descriptionForeground)]">
						Maximum number of lines to generate in a single completion
					</p>
				</div>
			</div>

			{/* Action Buttons */}
			<div className="flex gap-2 pt-4 border-t border-[var(--vscode-panel-border)]">
				<VSCodeButton onClick={handleSave} disabled={!hasUnsavedChanges} appearance="primary">
					Save Settings
				</VSCodeButton>
				<VSCodeButton onClick={handleReset} disabled={!hasUnsavedChanges} appearance="secondary">
					Reset
				</VSCodeButton>
				<VSCodeButton onClick={handleResetToDefaults} appearance="secondary">
					Reset to Defaults
				</VSCodeButton>
			</div>

			{/* Status Information */}
			<div className="mt-4 p-3 bg-[var(--vscode-textBlockQuote-background)] border-l-2 border-[var(--vscode-focusBorder)] rounded">
				<h5 className="text-sm font-medium text-[var(--vscode-foreground)] mb-2">Status</h5>
				<div className="text-xs text-[var(--vscode-descriptionForeground)] space-y-1">
					<div>Status: {settings.enabled ? "✅ Enabled" : "❌ Disabled"}</div>
					<div>Model: {settings.model}</div>
					<div>API Endpoint: {settings.baseUrl}</div>
					{hasUnsavedChanges && (
						<div className="text-[var(--vscode-notificationsWarningIcon-foreground)]">
							⚠️ You have unsaved changes
						</div>
					)}
				</div>
			</div>
		</div>
	)
}
