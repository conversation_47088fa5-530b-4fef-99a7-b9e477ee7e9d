# 自动完成配置页面修复

## 问题描述

自动完成配置页面的配置项无法保存，总是停留在默认值。用户修改配置后点击保存，但配置不会持久化到VS Code设置中。

## 问题原因

1. **缺少配置加载机制**: webview组件在初始化时没有从VS Code配置中加载当前设置，而是始终使用硬编码的默认值。

2. **缺少消息处理**: 扩展后端没有处理来自webview的`getAutocompleteSettings`请求消息。

3. **缺少响应机制**: 没有将当前配置发送回webview的机制。

## 修复方案

### 1. 添加配置请求消息类型

在 `src/shared/WebviewMessage.ts` 中添加新的消息类型：

```typescript
type:
    | "requestVsCodeLmModels"
    | "authStateChanged"
    // ... 其他类型
    | "autocompleteSettings"
    | "getAutocompleteSettings"  // 新增
```

### 2. 添加配置响应消息类型

在 `src/shared/ExtensionMessage.ts` 中添加响应类型：

```typescript
type:
    | "action"
    | "state"
    // ... 其他类型
    | "autocompleteSettingsResponse"  // 新增

// 添加设置数据结构
settings?: {
    enabled: boolean
    model: string
    apiKey: string
    baseUrl: string
    maxCompletions: number
    debounceMs: number
    maxLines: number
}
```

### 3. 修改webview组件加载逻辑

在 `webview-ui/src/components/settings/AutocompleteSettingsSection.tsx` 中：

```typescript
// 组件挂载时请求当前设置
useEffect(() => {
    vscode.postMessage({
        type: "getAutocompleteSettings",
    })
}, [])

// 监听来自扩展的设置响应
useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
        const message = event.data
        if (message.type === "autocompleteSettingsResponse" && message.settings) {
            setSettings(message.settings)
            setOriginalSettings(message.settings)
        }
    }

    window.addEventListener("message", handleMessage)
    return () => window.removeEventListener("message", handleMessage)
}, [])
```

### 4. 添加后端消息处理

在 `src/core/controller/index.ts` 中添加消息处理：

```typescript
case "getAutocompleteSettings": {
    await this.sendAutocompleteSettings()
    break
}
```

### 5. 实现配置发送方法

```typescript
async sendAutocompleteSettings() {
    // 从VS Code配置中读取当前设置
    const config = vscode.workspace.getConfiguration("qax-code.autocomplete")
    const settings = {
        enabled: config.get<boolean>("enabled", true),
        model: config.get<string>("model", "Qwen2.5-Coder-32B-Instruct"),
        apiKey: config.get<string>("apiKey", "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b"),
        baseUrl: config.get<string>("baseUrl", "https://aip.b.qianxin-inc.cn/v2"),
        maxCompletions: config.get<number>("maxCompletions", 5),
        debounceMs: config.get<number>("debounceMs", 250),
        maxLines: config.get<number>("maxLines", 100),
    }

    // 发送设置到webview
    await this.postMessageToWebview({
        type: "autocompleteSettingsResponse",
        settings,
    })
}
```

## 修复效果

1. **正确加载配置**: 页面打开时会显示当前VS Code中保存的配置值，而不是默认值。

2. **配置持久化**: 用户修改配置并保存后，设置会正确保存到VS Code配置中。

3. **状态同步**: 页面状态与实际配置保持同步。

## 测试验证

创建了单元测试来验证：
- 组件正确渲染
- 组件挂载时请求配置
- 消息监听器正确设置
- 配置更新时状态正确变化

测试文件: `webview-ui/src/components/settings/__tests__/AutocompleteSettingsSection.spec.tsx`

## 使用方法

1. 打开VS Code设置页面
2. 导航到"Autocomplete"标签页
3. 修改所需的配置项
4. 点击"Save Settings"按钮
5. 配置将被保存到VS Code设置中并在重新打开时保持

## 相关文件

- `webview-ui/src/components/settings/AutocompleteSettingsSection.tsx`
- `src/shared/WebviewMessage.ts`
- `src/shared/ExtensionMessage.ts`
- `src/core/controller/index.ts`
- `webview-ui/src/components/settings/__tests__/AutocompleteSettingsSection.spec.tsx`
