import * as vscode from "vscode"
import { build<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../api"
import { CodeContext, ContextGatherer } from "./ContextGatherer"
import { holeFillerTemplate } from "./templating/AutocompleteTemplate"
import { generateImportSnippets, generateDefinitionSnippets } from "./context/snippetProvider"
import { LRUCache } from "lru-cache"
import { createDebouncedFn } from "./utils/createDebouncedFn"
import { AutocompleteDecorationAnimation } from "./AutocompleteDecorationAnimation"
import { isHumanEdit } from "./utils/EditDetectionUtils"
import { ExperimentId } from "./types"

// Default values - these will be overridden by configuration
export const DEFAULT_UI_UPDATE_DEBOUNCE_MS = 250
export const DEFAULT_BAIL_OUT_TOO_MANY_LINES_LIMIT = 100
export const DEFAULT_MAX_COMPLETIONS_PER_CONTEXT = 5 // Per-given prefix/suffix lines, how many different per-line options to cache
export const DEFAULT_MODEL = "Qwen2.5-Coder-32B-Instruct"

export function processModelResponse(responseText: string): string {
	const fullMatch = /(<COMPLETION>)?([\s\S]*?)(<\/COMPLETION>|$)/.exec(responseText)
	if (!fullMatch) {
		return responseText
	}
	if (fullMatch[2].endsWith("</COMPLETION>")) {
		return fullMatch[2].slice(0, -"</COMPLETION>".length)
	}
	return fullMatch[2]
}

/**
 * Generates a cache key based on context's preceding and following lines
 * This is used to identify when we can reuse a previous completion
 */
function generateCacheKey({ precedingLines, followingLines }: CodeContext): string {
	const maxLinesToConsider = 5
	const precedingContext = precedingLines.slice(-maxLinesToConsider).join("\n")
	const followingContext = followingLines.slice(0, maxLinesToConsider).join("\n")
	return `${precedingContext}|||${followingContext}`
}

// Interface for autocomplete configuration
export interface AutocompleteConfig {
	enabled: boolean
	model: string
	apiKey: string
	baseUrl: string
	maxCompletions: number
	debounceMs: number
	maxLines: number
}

// Interface for getting autocomplete configuration
export interface AutocompleteConfigProvider {
	getAutocompleteConfig(): AutocompleteConfig
	getExperiments(): Record<ExperimentId, boolean>
}

/**
 * Sets up autocomplete with experiment flag checking.
 * This function periodically checks the experiment flag and registers/disposes
 * the autocomplete provider accordingly.
 */
export function registerAutocomplete(context: vscode.ExtensionContext, configProvider: AutocompleteConfigProvider): void {
	let autocompleteDisposable: vscode.Disposable | null = null
	let isCurrentlyEnabled = false

	// Function to check experiment flag and configuration and update provider
	const checkAndUpdateProvider = () => {
		const experiments = configProvider.getExperiments() ?? {}
		const experimentEnabled = experiments.autocomplete ?? false

		// Also check the configuration setting
		const config = configProvider.getAutocompleteConfig() ?? { enabled: true }
		const configEnabled = config.enabled

		// Autocomplete should be enabled if both experiment and config allow it
		const shouldBeEnabled = experimentEnabled && configEnabled

		// Only take action if the state has changed
		if (shouldBeEnabled !== isCurrentlyEnabled) {
			console.log(
				`🚀🔍 Autocomplete state changed to: ${shouldBeEnabled} (experiment: ${experimentEnabled}, config: ${configEnabled})`,
			)

			autocompleteDisposable?.dispose()
			autocompleteDisposable = shouldBeEnabled ? setupAutocomplete(context, configProvider) : null
			isCurrentlyEnabled = shouldBeEnabled
		}
	}

	checkAndUpdateProvider()
	const experimentCheckInterval = setInterval(checkAndUpdateProvider, 5000)

	// Make sure to clean up the interval when the extension is deactivated
	context.subscriptions.push({
		dispose: () => {
			clearInterval(experimentCheckInterval)
			autocompleteDisposable?.dispose()
		},
	})
}

function setupAutocomplete(context: vscode.ExtensionContext, configProvider: AutocompleteConfigProvider): vscode.Disposable {
	// Get configuration
	const config = configProvider.getAutocompleteConfig() ?? {
		enabled: true,
		model: DEFAULT_MODEL,
		apiKey: "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b",
		baseUrl: "https://aip.b.qianxin-inc.cn/v2",
		maxCompletions: DEFAULT_MAX_COMPLETIONS_PER_CONTEXT,
		debounceMs: DEFAULT_UI_UPDATE_DEBOUNCE_MS,
		maxLines: DEFAULT_BAIL_OUT_TOO_MANY_LINES_LIMIT,
	}

	// State
	let enabled = true // User toggle state (default to enabled)
	let activeRequestId: string | null = null
	let isBackspaceOperation = false // Flag to track backspace operations
	let justAcceptedSuggestion = false // Flag to track if a suggestion was just accepted
	let lastCompletionCost = 0 // Track the cost of the last completion
	let totalSessionCost = 0 // Track the total cost of all completions in the session

	// LRU Cache for completions
	const completionsCache = new LRUCache<string, string[]>({
		max: 50,
		ttl: 1000 * 60 * 60 * 24, // Cache for 24 hours
	})

	// Services
	const contextGatherer = new ContextGatherer()
	const animationManager = AutocompleteDecorationAnimation.getInstance()

	// Initialize API handler with configuration
	let apiHandler: ApiHandler | null = null

	apiHandler = buildApiHandler({
		apiProvider: "openai",
		openAiApiKey: config.apiKey,
		openAiBaseUrl: config.baseUrl,
		openAiModelId: config.model,
	})

	const clearState = () => {
		vscode.commands.executeCommand("editor.action.inlineSuggest.hide")
		animationManager.stopAnimation()

		isBackspaceOperation = false
		justAcceptedSuggestion = false
		activeRequestId = null
	}

	const generateCompletion = async ({
		codeContext,
		document,
		position,
	}: {
		codeContext: CodeContext
		document: vscode.TextDocument
		position: vscode.Position
	}) => {
		if (!apiHandler) {
			throw new Error("apiHandler must be set before calling generateCompletion!")
		}

		const requestId = crypto.randomUUID()
		activeRequestId = requestId
		animationManager.startAnimation()

		const snippets = [
			...generateImportSnippets(true, codeContext.imports, document.uri.fsPath),
			...generateDefinitionSnippets(true, codeContext.definitions),
		]
		const systemPrompt = holeFillerTemplate.getSystemPrompt()
		const userPrompt = holeFillerTemplate.template(codeContext, document, position, snippets)

		console.log(`🚀🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶\n`, { userPrompt })

		const stream = apiHandler.createMessage(systemPrompt, [{ role: "user", content: [{ type: "text", text: userPrompt }] }])

		let completion = ""
		let processedCompletion = ""
		let lineCount = 0
		let completionCost = 0

		try {
			for await (const chunk of stream) {
				if (activeRequestId !== requestId) {
					// Request was cancelled or superseded
					return null
				}

				if (chunk.type === "text") {
					completion += chunk.text
					processedCompletion = processModelResponse(completion)
					lineCount = processedCompletion.split("\n").length

					// Bail out if the completion is getting too long
					if (lineCount > config.maxLines) {
						console.log(`🚀🛑 Bailing out due to too many lines: ${lineCount}`)
						break
					}
				} else if (chunk.type === "usage") {
					completionCost = (chunk.inputTokens || 0) * 0.000003 + (chunk.outputTokens || 0) * 0.000015 // Rough cost calculation
				}
			}

			if (activeRequestId !== requestId) {
				return null
			}

			// Update costs
			lastCompletionCost = completionCost
			totalSessionCost += completionCost

			// Cache the completion
			const cacheKey = generateCacheKey(codeContext)
			const existingCompletions = completionsCache.get(cacheKey) ?? []
			if (!existingCompletions.includes(processedCompletion)) {
				existingCompletions.push(processedCompletion)
				if (existingCompletions.length > config.maxCompletions) {
					existingCompletions.shift() // Remove oldest
				}
				completionsCache.set(cacheKey, existingCompletions)
			}

			return { processedCompletion, cost: completionCost }
		} catch (error) {
			console.error("Error generating completion:", error)
			return null
		} finally {
			animationManager.stopAnimation()
		}
	}

	// Debounced version of generateCompletion
	const debouncedGenerateCompletion = createDebouncedFn(generateCompletion, config.debounceMs)

	const createInlineCompletionItem = (text: string, position: vscode.Position): vscode.InlineCompletionItem => {
		return {
			insertText: text,
			range: new vscode.Range(position, position),
		}
	}

	const provider: vscode.InlineCompletionItemProvider = {
		async provideInlineCompletionItems(document, position, context, token) {
			if (!enabled || !vscode.window.activeTextEditor) {
				return null
			}

			// Create or recreate the API handler if needed
			apiHandler =
				apiHandler ??
				buildApiHandler({
					apiProvider: "openai",
					openAiApiKey: config.apiKey,
					openAiBaseUrl: config.baseUrl,
					openAiModelId: config.model,
				})

			// Skip providing completions if this was triggered by a backspace operation of if we just accepted a suggestion
			if (isBackspaceOperation || justAcceptedSuggestion) {
				return null
			}

			// Get exactly what's been typed on the current line
			const linePrefix = document.getText(new vscode.Range(new vscode.Position(position.line, 0), position)).trimStart()
			console.log(`🚀🛑 Autocomplete for line with prefix: "${linePrefix}"!`)

			const codeContext = await contextGatherer.gatherContext(document, position, true, true)

			// Start animation early to show loading indicator
			animationManager.startAnimation()

			// Check if we have a cached completion for this context
			const cacheKey = generateCacheKey(codeContext)
			const cachedCompletions = completionsCache.get(cacheKey) ?? []
			for (const completion of cachedCompletions) {
				if (completion.startsWith(linePrefix)) {
					// Only show the remaining part of the completion
					const remainingSuffix = completion.substring(linePrefix.length)
					if (remainingSuffix.length > 0) {
						console.log(`🚀🎯 Using cached completions (${cachedCompletions.length} options)`)
						// Add a small delay before stopping animation so user can see it
						setTimeout(() => {
							animationManager.stopAnimation()
						}, 300)
						return [createInlineCompletionItem(remainingSuffix, position)]
					}
				}
			}

			const result = await debouncedGenerateCompletion({ document, codeContext, position })
			if (!result || token.isCancellationRequested) {
				return null
			}
			const { processedCompletion, cost } = result
			console.log(`🚀🛑🚀🛑🚀🛑🚀🛑🚀🛑 \n`, {
				processedCompletion,
				cost: formatCost(cost || 0),
			})

			if (processedCompletion && processedCompletion.trim().length > 0) {
				return [createInlineCompletionItem(processedCompletion, position)]
			}

			return null
		},
	}

	// Register provider and commands
	const providerDisposable = vscode.languages.registerInlineCompletionItemProvider({ pattern: "**" }, provider)

	// Status bar
	const statusBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100)
	statusBar.text = "$(sparkle) QAX Complete"
	statusBar.tooltip = "QAX Code Autocomplete"
	statusBar.command = "qax-code.toggleAutocomplete"
	statusBar.show()

	// Helper function to format cost with special handling for small amounts
	const formatCost = (cost: number): string => {
		if (cost === 0) {
			return "$0.00"
		}
		if (cost > 0 && cost < 0.01) {
			return "<$0.01" // Less than one cent
		}
		return `$${cost.toFixed(2)}`
	}

	const updateStatusBar = () => {
		const totalCostFormatted = formatCost(totalSessionCost)
		statusBar.text = enabled ? "$(sparkle) QAX Complete" : "$(sparkle-off) QAX Complete"
		statusBar.tooltip = `QAX Code Autocomplete ${enabled ? "enabled" : "disabled"}

Last completion: $${lastCompletionCost.toFixed(5)}
Session total cost: ${totalCostFormatted}
Model: ${config.model}`
	}

	const toggleCommand = vscode.commands.registerCommand("qax-code.toggleAutocomplete", () => {
		enabled = !enabled
		updateStatusBar()
		vscode.window.showInformationMessage(`QAX Complete ${enabled ? "enabled" : "disabled"}`)
	})

	// Command to track when a suggestion is accepted
	const trackAcceptedSuggestionCommand = vscode.commands.registerCommand("qax-code.trackAcceptedSuggestion", () => {
		justAcceptedSuggestion = true
	})

	// Track text document changes to detect backspace operations
	const textDocumentChangeListener = vscode.workspace.onDidChangeTextDocument((event) => {
		if (event.document === vscode.window.activeTextEditor?.document) {
			for (const change of event.contentChanges) {
				if (isHumanEdit(change)) {
					if (change.text === "" && change.rangeLength > 0) {
						// This is a deletion (backspace/delete)
						isBackspaceOperation = true
						// Reset the flag after a short delay
						setTimeout(() => {
							isBackspaceOperation = false
						}, 100)
					}
				}
			}
		}
	})

	// Track when inline suggestions are accepted
	const inlineSuggestionAcceptedListener = vscode.commands.registerCommand("editor.action.inlineSuggest.commit", () => {
		justAcceptedSuggestion = true
		// Reset the flag after a short delay
		setTimeout(() => {
			justAcceptedSuggestion = false
		}, 100)
	})

	updateStatusBar()

	return new vscode.Disposable(() => {
		clearState()
		providerDisposable.dispose()
		statusBar.dispose()
		toggleCommand.dispose()
		trackAcceptedSuggestionCommand.dispose()
		textDocumentChangeListener.dispose()
		inlineSuggestionAcceptedListener.dispose()
	})
}
