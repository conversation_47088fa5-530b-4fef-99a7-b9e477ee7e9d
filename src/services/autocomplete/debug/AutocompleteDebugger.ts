import * as vscode from "vscode"

/**
 * QAX Code Autocomplete 调试工具
 * 用于诊断和调试 autocomplete 功能
 */
export class AutocompleteDebugger {
	private static instance: AutocompleteDebugger
	private outputChannel: vscode.OutputChannel
	private diagnosticCollection: vscode.DiagnosticCollection

	private constructor() {
		this.outputChannel = vscode.window.createOutputChannel("QAX Autocomplete Debug")
		this.diagnosticCollection = vscode.languages.createDiagnosticCollection("qax-autocomplete")
	}

	public static getInstance(): AutocompleteDebugger {
		if (!AutocompleteDebugger.instance) {
			AutocompleteDebugger.instance = new AutocompleteDebugger()
		}
		return AutocompleteDebugger.instance
	}

	/**
	 * 记录调试信息
	 */
	public log(message: string, level: "info" | "warn" | "error" = "info"): void {
		const timestamp = new Date().toISOString()
		const prefix = {
			info: "📋",
			warn: "⚠️",
			error: "❌",
		}[level]

		const logMessage = `${prefix} [${timestamp}] ${message}`
		this.outputChannel.appendLine(logMessage)
		console.log(logMessage)
	}

	/**
	 * 显示调试输出面板
	 */
	public show(): void {
		this.outputChannel.show()
	}

	/**
	 * 检查 autocomplete 配置
	 */
	public checkConfiguration(): void {
		this.log("🔍 Checking QAX Autocomplete configuration...")

		try {
			const config = vscode.workspace.getConfiguration("qax-code.autocomplete")
			const experiments = vscode.workspace.getConfiguration("qax-code.experiments")

			this.log(`Enabled: ${config.get("enabled")}`)
			this.log(`Model: ${config.get("model")}`)
			this.log(`Base URL: ${config.get("baseUrl")}`)
			this.log(`API Key: ${config.get("apiKey") ? "***configured***" : "not set"}`)
			this.log(`Max Completions: ${config.get("maxCompletions")}`)
			this.log(`Debounce MS: ${config.get("debounceMs")}`)
			this.log(`Max Lines: ${config.get("maxLines")}`)

			this.log(`Experiment - Autocomplete: ${experiments.get("autocomplete")}`)
			this.log(`Experiment - Enhanced Context: ${experiments.get("enhancedContext")}`)
			this.log(`Experiment - Multi-line: ${experiments.get("multiLineCompletion")}`)
			this.log(`Experiment - Smart Caching: ${experiments.get("smartCaching")}`)

			this.log("✅ Configuration check completed")
		} catch (error) {
			this.log(`❌ Configuration check failed: ${error}`, "error")
		}
	}

	/**
	 * 检查扩展状态
	 */
	public checkExtensionStatus(): void {
		this.log("🔍 Checking extension status...")

		try {
			// 检查扩展是否激活
			const extension = vscode.extensions.getExtension("saoudrizwan.claude-dev") // 替换为实际的扩展 ID
			if (extension) {
				this.log(`Extension found: ${extension.id}`)
				this.log(`Extension active: ${extension.isActive}`)
				this.log(`Extension version: ${extension.packageJSON.version}`)
			} else {
				this.log("❌ Extension not found", "error")
			}

			// 检查命令是否注册
			vscode.commands.getCommands().then((commands) => {
				const autocompleteCommands = commands.filter((cmd) => cmd.includes("qax-code"))
				this.log(`Registered QAX commands: ${autocompleteCommands.join(", ")}`)
			})

			this.log("✅ Extension status check completed")
		} catch (error) {
			this.log(`❌ Extension status check failed: ${error}`, "error")
		}
	}

	/**
	 * 检查当前文档上下文
	 */
	public checkCurrentContext(): void {
		this.log("🔍 Checking current document context...")

		try {
			const editor = vscode.window.activeTextEditor
			if (!editor) {
				this.log("❌ No active editor", "warn")
				return
			}

			const document = editor.document
			const position = editor.selection.active

			this.log(`File: ${document.fileName}`)
			this.log(`Language: ${document.languageId}`)
			this.log(`Line count: ${document.lineCount}`)
			this.log(`Current position: ${position.line}:${position.character}`)

			// 获取当前行内容
			const currentLine = document.lineAt(position.line).text
			this.log(`Current line: "${currentLine}"`)

			// 获取前后几行作为上下文
			const contextLines = 3
			const startLine = Math.max(0, position.line - contextLines)
			const endLine = Math.min(document.lineCount - 1, position.line + contextLines)

			this.log("Context:")
			for (let i = startLine; i <= endLine; i++) {
				const line = document.lineAt(i).text
				const marker = i === position.line ? " >>> " : "     "
				this.log(`${marker}${i + 1}: ${line}`)
			}

			this.log("✅ Context check completed")
		} catch (error) {
			this.log(`❌ Context check failed: ${error}`, "error")
		}
	}

	/**
	 * 测试 API 连接
	 */
	public async testApiConnection(): Promise<void> {
		this.log("🔍 Testing API connection...")

		try {
			const config = vscode.workspace.getConfiguration("qax-code.autocomplete")
			const baseUrl = config.get<string>("baseUrl")
			const apiKey = config.get<string>("apiKey")

			if (!baseUrl || !apiKey) {
				this.log("❌ API configuration incomplete", "error")
				return
			}

			this.log(`Testing connection to: ${baseUrl}`)

			// 这里可以添加实际的 API 测试逻辑
			// 由于我们使用的是 buildApiHandler，这里只是模拟测试
			this.log("⚠️ API connection test not implemented yet", "warn")
			this.log("✅ API test completed (simulated)")
		} catch (error) {
			this.log(`❌ API test failed: ${error}`, "error")
		}
	}

	/**
	 * 运行完整的诊断
	 */
	public async runFullDiagnostic(): Promise<void> {
		this.log("🚀 Starting QAX Autocomplete full diagnostic...")
		this.show()

		this.checkConfiguration()
		this.checkExtensionStatus()
		this.checkCurrentContext()
		await this.testApiConnection()

		this.log("🎉 Full diagnostic completed!")
	}

	/**
	 * 创建诊断报告
	 */
	public createDiagnosticReport(): string {
		const config = vscode.workspace.getConfiguration("qax-code.autocomplete")
		const experiments = vscode.workspace.getConfiguration("qax-code.experiments")
		const editor = vscode.window.activeTextEditor

		const report = `
# QAX Code Autocomplete 诊断报告

## 配置信息
- 启用状态: ${config.get("enabled")}
- 模型: ${config.get("model")}
- API Base URL: ${config.get("baseUrl")}
- API Key: ${config.get("apiKey") ? "已配置" : "未配置"}
- 最大补全数: ${config.get("maxCompletions")}
- 防抖延迟: ${config.get("debounceMs")}ms
- 最大行数: ${config.get("maxLines")}

## 实验性功能
- Autocomplete: ${experiments.get("autocomplete")}
- Enhanced Context: ${experiments.get("enhancedContext")}
- Multi-line Completion: ${experiments.get("multiLineCompletion")}
- Smart Caching: ${experiments.get("smartCaching")}

## 当前环境
- VS Code 版本: ${vscode.version}
- 活动编辑器: ${editor ? editor.document.fileName : "无"}
- 当前语言: ${editor ? editor.document.languageId : "无"}
- 当前位置: ${editor ? `${editor.selection.active.line}:${editor.selection.active.character}` : "无"}

## 生成时间
${new Date().toISOString()}
		`.trim()

		return report
	}

	/**
	 * 保存诊断报告到文件
	 */
	public async saveDiagnosticReport(): Promise<void> {
		try {
			const report = this.createDiagnosticReport()
			const uri = await vscode.window.showSaveDialog({
				defaultUri: vscode.Uri.file("qax-autocomplete-diagnostic.md"),
				filters: {
					Markdown: ["md"],
					"Text files": ["txt"],
				},
			})

			if (uri) {
				await vscode.workspace.fs.writeFile(uri, Buffer.from(report, "utf8"))
				this.log(`✅ Diagnostic report saved to: ${uri.fsPath}`)
				vscode.window.showInformationMessage("诊断报告已保存")
			}
		} catch (error) {
			this.log(`❌ Failed to save diagnostic report: ${error}`, "error")
		}
	}

	/**
	 * 清理资源
	 */
	public dispose(): void {
		this.outputChannel.dispose()
		this.diagnosticCollection.dispose()
	}
}

/**
 * 注册调试命令
 */
export function registerDebugCommands(context: vscode.ExtensionContext): void {
	const debugInstance = AutocompleteDebugger.getInstance()

	const commands = [
		vscode.commands.registerCommand("qax-code.debug.showOutput", () => {
			debugInstance.show()
		}),
		vscode.commands.registerCommand("qax-code.debug.checkConfig", () => {
			debugInstance.checkConfiguration()
		}),
		vscode.commands.registerCommand("qax-code.debug.checkStatus", () => {
			debugInstance.checkExtensionStatus()
		}),
		vscode.commands.registerCommand("qax-code.debug.checkContext", () => {
			debugInstance.checkCurrentContext()
		}),
		vscode.commands.registerCommand("qax-code.debug.testApi", () => {
			debugInstance.testApiConnection()
		}),
		vscode.commands.registerCommand("qax-code.debug.runDiagnostic", () => {
			debugInstance.runFullDiagnostic()
		}),
		vscode.commands.registerCommand("qax-code.debug.saveDiagnostic", () => {
			debugInstance.saveDiagnosticReport()
		}),
	]

	context.subscriptions.push(...commands)
}
