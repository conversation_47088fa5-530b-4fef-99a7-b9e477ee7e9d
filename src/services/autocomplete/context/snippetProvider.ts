import * as path from "path"

/**
 * Generates import-related code snippets for context
 */
export function generateImportSnippets(enabled: boolean, imports: string[], currentFilePath: string): string[] {
	if (!enabled || imports.length === 0) {
		return []
	}

	const snippets: string[] = []
	const fileExtension = path.extname(currentFilePath)
	const language = getLanguageFromExtension(fileExtension)

	// Add import statements as context
	snippets.push("// Import statements:")
	imports.forEach((importStatement) => {
		snippets.push(importStatement)
	})

	// Generate common import patterns based on language
	switch (language) {
		case "typescript":
		case "javascript":
			snippets.push(...generateJSImportPatterns(imports))
			break
		case "python":
			snippets.push(...generatePythonImportPatterns(imports))
			break
		case "java":
			snippets.push(...generateJavaImportPatterns(imports))
			break
		case "csharp":
			snippets.push(...generateCSharpImportPatterns(imports))
			break
	}

	return snippets
}

/**
 * Generates definition-related code snippets for context
 */
export function generateDefinitionSnippets(enabled: boolean, definitions: string[]): string[] {
	if (!enabled || definitions.length === 0) {
		return []
	}

	const snippets: string[] = []

	// Add definitions as context
	snippets.push("// Available definitions:")
	definitions.forEach((definition) => {
		snippets.push(definition)
	})

	return snippets
}

function getLanguageFromExtension(extension: string): string {
	const languageMap: Record<string, string> = {
		".ts": "typescript",
		".tsx": "typescript",
		".js": "javascript",
		".jsx": "javascript",
		".py": "python",
		".java": "java",
		".cs": "csharp",
		".go": "go",
		".rs": "rust",
		".cpp": "cpp",
		".c": "c",
		".h": "c",
		".hpp": "cpp",
	}

	return languageMap[extension.toLowerCase()] || "unknown"
}

function generateJSImportPatterns(imports: string[]): string[] {
	const patterns: string[] = []

	// Extract module names from imports
	const modules = imports
		.map((imp) => {
			const match = imp.match(/from\s+['"`]([^'"`]+)['"`]/)
			return match ? match[1] : null
		})
		.filter(Boolean) as string[]

	// Generate common patterns
	if (modules.some((mod) => mod.includes("react"))) {
		patterns.push("// React patterns:")
		patterns.push("import React, { useState, useEffect } from 'react'")
		patterns.push("const [state, setState] = useState()")
		patterns.push("useEffect(() => {}, [])")
	}

	if (modules.some((mod) => mod.includes("express"))) {
		patterns.push("// Express patterns:")
		patterns.push("app.get('/', (req, res) => {})")
		patterns.push("app.post('/', (req, res) => {})")
	}

	return patterns
}

function generatePythonImportPatterns(imports: string[]): string[] {
	const patterns: string[] = []

	// Extract module names from imports
	const modules = imports
		.map((imp) => {
			const match = imp.match(/(?:import\s+(\w+)|from\s+(\w+))/)
			return match ? match[1] || match[2] : null
		})
		.filter(Boolean) as string[]

	// Generate common patterns
	if (modules.some((mod) => mod.includes("numpy"))) {
		patterns.push("// NumPy patterns:")
		patterns.push("np.array([])")
		patterns.push("np.zeros(shape)")
	}

	if (modules.some((mod) => mod.includes("pandas"))) {
		patterns.push("// Pandas patterns:")
		patterns.push("pd.DataFrame()")
		patterns.push("df.head()")
	}

	if (modules.some((mod) => mod.includes("flask"))) {
		patterns.push("// Flask patterns:")
		patterns.push("@app.route('/')")
		patterns.push("def index():")
	}

	return patterns
}

function generateJavaImportPatterns(imports: string[]): string[] {
	const patterns: string[] = []

	// Extract package names from imports
	const packages = imports
		.map((imp) => {
			const match = imp.match(/import\s+([\w.]+)/)
			return match ? match[1] : null
		})
		.filter(Boolean) as string[]

	// Generate common patterns
	if (packages.some((pkg) => pkg.includes("java.util"))) {
		patterns.push("// Java Collections patterns:")
		patterns.push("List<String> list = new ArrayList<>()")
		patterns.push("Map<String, Object> map = new HashMap<>()")
	}

	if (packages.some((pkg) => pkg.includes("springframework"))) {
		patterns.push("// Spring patterns:")
		patterns.push("@Autowired")
		patterns.push("@Service")
		patterns.push("@Controller")
	}

	return patterns
}

function generateCSharpImportPatterns(imports: string[]): string[] {
	const patterns: string[] = []

	// Extract namespace names from imports
	const namespaces = imports
		.map((imp) => {
			const match = imp.match(/using\s+([\w.]+)/)
			return match ? match[1] : null
		})
		.filter(Boolean) as string[]

	// Generate common patterns
	if (namespaces.some((ns) => ns.includes("System.Collections"))) {
		patterns.push("// Collections patterns:")
		patterns.push("List<string> list = new List<string>()")
		patterns.push("Dictionary<string, object> dict = new Dictionary<string, object>()")
	}

	if (namespaces.some((ns) => ns.includes("Microsoft.AspNetCore"))) {
		patterns.push("// ASP.NET Core patterns:")
		patterns.push("[HttpGet]")
		patterns.push("[HttpPost]")
		patterns.push("public IActionResult Index()")
	}

	return patterns
}

/**
 * Generates context-aware code snippets based on the current code context
 */
export function generateContextualSnippets(language: string, currentLine: string, precedingLines: string[]): string[] {
	const snippets: string[] = []

	// Analyze the current context and suggest relevant snippets
	const context = analyzeCodeContext(currentLine, precedingLines)

	switch (language) {
		case "typescript":
		case "javascript":
			snippets.push(...generateJSContextualSnippets(context))
			break
		case "python":
			snippets.push(...generatePythonContextualSnippets(context))
			break
		case "java":
			snippets.push(...generateJavaContextualSnippets(context))
			break
	}

	return snippets
}

interface CodeContext {
	isInFunction: boolean
	isInClass: boolean
	isInLoop: boolean
	isInConditional: boolean
	indentationLevel: number
	nearbyVariables: string[]
}

function analyzeCodeContext(currentLine: string, precedingLines: string[]): CodeContext {
	const context: CodeContext = {
		isInFunction: false,
		isInClass: false,
		isInLoop: false,
		isInConditional: false,
		indentationLevel: 0,
		nearbyVariables: [],
	}

	// Analyze indentation
	const match = currentLine.match(/^(\s*)/)
	context.indentationLevel = match ? match[1].length : 0

	// Analyze preceding lines for context
	const recentLines = precedingLines.slice(-10)
	for (const line of recentLines) {
		if (line.includes("function") || line.includes("def ") || line.includes("=>")) {
			context.isInFunction = true
		}
		if (line.includes("class ")) {
			context.isInClass = true
		}
		if (line.includes("for ") || line.includes("while ")) {
			context.isInLoop = true
		}
		if (line.includes("if ") || line.includes("else")) {
			context.isInConditional = true
		}

		// Extract variable names
		const varMatches = line.match(/(?:const|let|var)\s+(\w+)/g)
		if (varMatches) {
			context.nearbyVariables.push(...varMatches.map((match) => match.split(/\s+/)[1]))
		}
	}

	return context
}

function generateJSContextualSnippets(context: CodeContext): string[] {
	const snippets: string[] = []

	if (context.isInFunction) {
		snippets.push("return ")
		snippets.push("console.log()")
	}

	if (context.isInLoop) {
		snippets.push("break")
		snippets.push("continue")
	}

	if (context.nearbyVariables.length > 0) {
		snippets.push(`// Available variables: ${context.nearbyVariables.join(", ")}`)
	}

	return snippets
}

function generatePythonContextualSnippets(context: CodeContext): string[] {
	const snippets: string[] = []

	if (context.isInFunction) {
		snippets.push("return ")
		snippets.push("print()")
	}

	if (context.isInLoop) {
		snippets.push("break")
		snippets.push("continue")
	}

	return snippets
}

function generateJavaContextualSnippets(context: CodeContext): string[] {
	const snippets: string[] = []

	if (context.isInFunction) {
		snippets.push("return ")
		snippets.push("System.out.println()")
	}

	if (context.isInLoop) {
		snippets.push("break;")
		snippets.push("continue;")
	}

	return snippets
}
