/**
 * Creates a debounced version of a function that delays invoking the function
 * until after wait milliseconds have elapsed since the last time the debounced
 * function was invoked.
 */
export function createDebouncedFn<T extends (...args: any[]) => any>(
	fn: T,
	wait: number,
): (...args: Parameters<T>) => Promise<ReturnType<T> | null> {
	let timeoutId: NodeJS.Timeout | null = null
	let latestArgs: Parameters<T> | null = null
	let latestResolve: ((value: ReturnType<T> | null) => void) | null = null

	return (...args: Parameters<T>): Promise<ReturnType<T> | null> => {
		return new Promise((resolve) => {
			// Store the latest arguments and resolve function
			latestArgs = args
			latestResolve = resolve

			// Clear any existing timeout
			if (timeoutId) {
				clearTimeout(timeoutId)
			}

			// Set a new timeout
			timeoutId = setTimeout(async () => {
				if (latestArgs && latestResolve) {
					try {
						const result = await fn(...latestArgs)
						latestResolve(result)
					} catch (error) {
						console.error("Error in debounced function:", error)
						latestResolve(null)
					}
				}

				// Reset state
				timeoutId = null
				latestArgs = null
				latestResolve = null
			}, wait)
		})
	}
}

/**
 * Creates a throttled version of a function that only invokes the function
 * at most once per every wait milliseconds.
 */
export function createThrottledFn<T extends (...args: any[]) => any>(
	fn: T,
	wait: number,
): (...args: Parameters<T>) => Promise<ReturnType<T> | null> {
	let lastCallTime = 0
	let timeoutId: NodeJS.Timeout | null = null
	let latestArgs: Parameters<T> | null = null

	return (...args: Parameters<T>): Promise<ReturnType<T> | null> => {
		return new Promise((resolve) => {
			const now = Date.now()
			latestArgs = args

			const callFunction = async () => {
				lastCallTime = Date.now()
				if (latestArgs) {
					try {
						const result = await fn(...latestArgs)
						resolve(result)
					} catch (error) {
						console.error("Error in throttled function:", error)
						resolve(null)
					}
				} else {
					resolve(null)
				}
			}

			if (now - lastCallTime >= wait) {
				// Enough time has passed, call immediately
				callFunction()
			} else {
				// Not enough time has passed, schedule for later
				if (timeoutId) {
					clearTimeout(timeoutId)
				}

				const remainingTime = wait - (now - lastCallTime)
				timeoutId = setTimeout(callFunction, remainingTime)
			}
		})
	}
}
