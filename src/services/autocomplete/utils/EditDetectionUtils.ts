import * as vscode from "vscode"

/**
 * Determines if a text document change was made by a human user
 * (as opposed to programmatic changes like auto-formatting, extensions, etc.)
 */
export function isHumanEdit(change: vscode.TextDocumentContentChangeEvent): boolean {
	// Check if the change is a simple insertion or deletion
	// Human edits are typically single character insertions/deletions or small text changes

	// Very large changes are likely programmatic (like auto-formatting)
	if (change.text.length > 1000 || change.rangeLength > 1000) {
		return false
	}

	// Empty text with range length > 0 indicates deletion
	if (change.text === "" && change.rangeLength > 0) {
		return true // Deletion (backspace/delete key)
	}

	// Single character insertion
	if (change.text.length === 1 && change.rangeLength === 0) {
		return true
	}

	// Small text insertions (like pasting a word or auto-completion acceptance)
	if (change.text.length <= 100 && change.rangeLength === 0) {
		return true
	}

	// Small text replacements
	if (change.text.length <= 100 && change.rangeLength <= 100) {
		return true
	}

	// Multi-line changes that are not too large might be human
	const lineCount = change.text.split("\n").length
	if (lineCount <= 10 && change.text.length <= 500) {
		return true
	}

	// Default to false for other cases (likely programmatic)
	return false
}

/**
 * Determines if a change represents a backspace or delete operation
 */
export function isBackspaceOrDelete(change: vscode.TextDocumentContentChangeEvent): boolean {
	return change.text === "" && change.rangeLength > 0
}

/**
 * Determines if a change represents text insertion
 */
export function isTextInsertion(change: vscode.TextDocumentContentChangeEvent): boolean {
	return change.text.length > 0 && change.rangeLength === 0
}

/**
 * Determines if a change represents text replacement
 */
export function isTextReplacement(change: vscode.TextDocumentContentChangeEvent): boolean {
	return change.text.length > 0 && change.rangeLength > 0
}

/**
 * Gets the type of edit operation
 */
export type EditType = "insertion" | "deletion" | "replacement" | "unknown"

export function getEditType(change: vscode.TextDocumentContentChangeEvent): EditType {
	if (isTextInsertion(change)) {
		return "insertion"
	}
	if (isBackspaceOrDelete(change)) {
		return "deletion"
	}
	if (isTextReplacement(change)) {
		return "replacement"
	}
	return "unknown"
}

/**
 * Determines if the change is likely from auto-completion acceptance
 */
export function isAutoCompletionAcceptance(change: vscode.TextDocumentContentChangeEvent): boolean {
	// Auto-completion acceptance typically involves:
	// 1. Inserting text (no replacement)
	// 2. Text is longer than a single character
	// 3. Text doesn't contain newlines (usually single line completions)
	// 4. Text is not extremely long (reasonable completion size)

	return (
		change.rangeLength === 0 && // No text replaced
		change.text.length > 1 && // More than single character
		change.text.length <= 200 && // Reasonable completion size
		!change.text.includes("\n") // Single line completion
	)
}

/**
 * Determines if the change is likely from snippet expansion
 */
export function isSnippetExpansion(change: vscode.TextDocumentContentChangeEvent): boolean {
	// Snippet expansion typically involves:
	// 1. Multi-line text insertion
	// 2. Contains placeholder patterns like $1, $2, etc.
	// 3. Moderate size (not too large)

	return (
		change.rangeLength === 0 && // No text replaced
		change.text.includes("\n") && // Multi-line
		change.text.length <= 1000 && // Reasonable snippet size
		(/\$\d+|\$\{[^}]+\}/.test(change.text) || // Contains snippet placeholders
			change.text.includes("\t")) // Contains tab characters (common in snippets)
	)
}

/**
 * Determines if the change is likely from auto-formatting
 */
export function isAutoFormatting(change: vscode.TextDocumentContentChangeEvent): boolean {
	// Auto-formatting typically involves:
	// 1. Large text replacement
	// 2. Primarily whitespace changes
	// 3. Multiple lines affected

	if (change.rangeLength === 0) {
		return false // Auto-formatting usually replaces text
	}

	// Check if the change is primarily whitespace adjustments
	const textWithoutWhitespace = change.text.replace(/\s/g, "")
	const originalLength = change.rangeLength

	// If most of the change is whitespace, it's likely formatting
	const whitespaceRatio = (change.text.length - textWithoutWhitespace.length) / change.text.length

	return (
		whitespaceRatio > 0.3 && // At least 30% whitespace changes
		originalLength > 50 && // Affects a reasonable amount of text
		change.text.includes("\n") // Multi-line change
	)
}

/**
 * Comprehensive analysis of a text change
 */
export interface ChangeAnalysis {
	type: EditType
	isHuman: boolean
	isAutoCompletion: boolean
	isSnippet: boolean
	isFormatting: boolean
	isBackspace: boolean
	characterCount: number
	lineCount: number
}

export function analyzeChange(change: vscode.TextDocumentContentChangeEvent): ChangeAnalysis {
	return {
		type: getEditType(change),
		isHuman: isHumanEdit(change),
		isAutoCompletion: isAutoCompletionAcceptance(change),
		isSnippet: isSnippetExpansion(change),
		isFormatting: isAutoFormatting(change),
		isBackspace: isBackspaceOrDelete(change),
		characterCount: change.text.length,
		lineCount: change.text.split("\n").length,
	}
}
