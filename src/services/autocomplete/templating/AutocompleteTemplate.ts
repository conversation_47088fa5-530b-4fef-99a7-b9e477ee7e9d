import * as vscode from "vscode"
import { CodeContext } from "../ContextGatherer"

export interface AutocompleteTemplate {
	getSystemPrompt(): string
	template(context: CodeContext, document: vscode.TextDocument, position: vscode.Position, snippets: string[]): string
}

class HoleFillerTemplate implements AutocompleteTemplate {
	getSystemPrompt(): string {
		return `You are an AI coding assistant that provides intelligent code completions. Your task is to complete the code at the cursor position based on the surrounding context.

Guidelines:
1. Analyze the code context, including imports, function definitions, and surrounding code
2. Provide a completion that fits naturally with the existing code style and patterns
3. Consider the programming language conventions and best practices
4. Keep completions concise and relevant to the immediate context
5. Do not include explanations or comments unless they are part of the natural code flow
6. Ensure proper indentation and formatting that matches the existing code
7. Focus on completing the current line or logical block of code

Your response should contain only the code completion, wrapped in <COMPLETION> tags.`
	}

	template(context: CodeContext, document: vscode.TextDocument, position: vscode.Position, snippets: string[]): string {
		const { precedingLines, followingLines, imports, definitions, currentLine, indentation, language, filePath } = context

		// Get the current line up to the cursor position
		const linePrefix = document.getText(new vscode.Range(new vscode.Position(position.line, 0), position))
		const lineSuffix = document.getText(
			new vscode.Range(position, new vscode.Position(position.line, document.lineAt(position.line).text.length)),
		)

		// Build the context string
		let contextStr = `File: ${filePath}\nLanguage: ${language}\n\n`

		// Add imports if available
		if (imports.length > 0) {
			contextStr += "Imports:\n"
			imports.forEach((imp) => {
				contextStr += `${imp}\n`
			})
			contextStr += "\n"
		}

		// Add relevant definitions if available
		if (definitions.length > 0) {
			contextStr += "Definitions:\n"
			definitions.forEach((def) => {
				contextStr += `${def}\n`
			})
			contextStr += "\n"
		}

		// Add code snippets if available
		if (snippets.length > 0) {
			contextStr += "Related code snippets:\n"
			snippets.forEach((snippet) => {
				contextStr += `${snippet}\n`
			})
			contextStr += "\n"
		}

		// Add preceding context
		contextStr += "Code before cursor:\n"
		const contextLines = precedingLines.slice(-20) // Last 20 lines for context
		contextLines.forEach((line, index) => {
			contextStr += `${contextLines.length - 20 + index + 1}: ${line}\n`
		})

		// Add current line with cursor position marked
		contextStr += `${precedingLines.length + 1}: ${linePrefix}<CURSOR>${lineSuffix}\n`

		// Add following context
		if (followingLines.length > 0) {
			contextStr += "\nCode after cursor:\n"
			followingLines.slice(0, 10).forEach((line, index) => {
				contextStr += `${precedingLines.length + 2 + index}: ${line}\n`
			})
		}

		// Add completion request
		contextStr += `\nPlease complete the code at the <CURSOR> position. Consider:
- The current indentation level: "${indentation}"
- The programming language: ${language}
- The existing code patterns and style
- The logical flow and context

Provide only the code that should be inserted at the cursor position, maintaining proper formatting and indentation.`

		return contextStr
	}
}

// Export the singleton instance
export const holeFillerTemplate = new HoleFillerTemplate()

// Additional template for different completion styles
class LineCompletionTemplate implements AutocompleteTemplate {
	getSystemPrompt(): string {
		return `You are an AI coding assistant specialized in completing single lines of code. Your task is to complete the current line based on the context.

Guidelines:
1. Focus on completing the current line only
2. Maintain consistency with the existing code style
3. Consider variable names, function calls, and patterns from the surrounding code
4. Ensure syntactic correctness for the programming language
5. Keep completions concise and directly relevant

Your response should contain only the line completion, wrapped in <COMPLETION> tags.`
	}

	template(context: CodeContext, document: vscode.TextDocument, position: vscode.Position, snippets: string[]): string {
		const { precedingLines, currentLine, language } = context

		// Get the current line up to the cursor position
		const linePrefix = document.getText(new vscode.Range(new vscode.Position(position.line, 0), position))

		// Build a focused context for line completion
		let contextStr = `Language: ${language}\n\n`

		// Add recent context (last 5 lines)
		contextStr += "Recent code:\n"
		const recentLines = precedingLines.slice(-5)
		recentLines.forEach((line, index) => {
			contextStr += `${line}\n`
		})

		// Add current line prefix
		contextStr += `Current line: ${linePrefix}<CURSOR>\n`

		// Add completion request
		contextStr += `\nComplete the current line starting from <CURSOR>. Provide only the remaining part of the line.`

		return contextStr
	}
}

export const lineCompletionTemplate = new LineCompletionTemplate()

// Template factory for different completion modes
export class TemplateFactory {
	static getTemplate(mode: "hole-filler" | "line-completion" = "hole-filler"): AutocompleteTemplate {
		switch (mode) {
			case "line-completion":
				return lineCompletionTemplate
			case "hole-filler":
			default:
				return holeFillerTemplate
		}
	}
}
