/**
 * Experiment identifiers for feature flags
 */
export type ExperimentId = "autocomplete" | "enhanced-context" | "multi-line-completion" | "smart-caching"

/**
 * Configuration for autocomplete functionality
 */
export interface AutocompleteSettings {
	enabled: boolean
	model: string
	apiKey: string
	baseUrl: string
	maxCompletions: number
	debounceMs: number
	maxLines: number
	enableEnhancedContext: boolean
	enableMultiLineCompletion: boolean
	enableSmartCaching: boolean
}

/**
 * Default autocomplete settings
 */
export const DEFAULT_AUTOCOMPLETE_SETTINGS: AutocompleteSettings = {
	enabled: true,
	model: "Qwen2.5-Coder-32B-Instruct",
	apiKey: "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b",
	baseUrl: "https://aip.b.qianxin-inc.cn/v2",
	maxCompletions: 5,
	debounceMs: 250,
	maxLines: 100,
	enableEnhancedContext: true,
	enableMultiLineCompletion: true,
	enableSmartCaching: true,
}

/**
 * Completion request context
 */
export interface CompletionContext {
	filePath: string
	language: string
	position: {
		line: number
		character: number
	}
	precedingText: string
	followingText: string
	imports: string[]
	definitions: string[]
}

/**
 * Completion response
 */
export interface CompletionResponse {
	text: string
	confidence: number
	cost: number
	cached: boolean
	model: string
	timestamp: number
}

/**
 * Completion statistics
 */
export interface CompletionStats {
	totalCompletions: number
	acceptedCompletions: number
	rejectedCompletions: number
	totalCost: number
	averageLatency: number
	cacheHitRate: number
}

/**
 * Autocomplete provider status
 */
export type AutocompleteStatus = "idle" | "loading" | "error" | "disabled"

/**
 * Status bar information
 */
export interface StatusBarInfo {
	status: AutocompleteStatus
	model: string
	lastCompletionCost: number
	sessionCost: number
	completionCount: number
	enabled: boolean
}

/**
 * Language-specific configuration
 */
export interface LanguageConfig {
	language: string
	maxContextLines: number
	importPatterns: RegExp[]
	definitionPatterns: RegExp[]
	commentPatterns: RegExp[]
	indentationStyle: "spaces" | "tabs"
	indentationSize: number
}

/**
 * Common language configurations
 */
export const LANGUAGE_CONFIGS: Record<string, LanguageConfig> = {
	typescript: {
		language: "typescript",
		maxContextLines: 50,
		importPatterns: [/^import\s+.*?from\s+['"`].*?['"`]/],
		definitionPatterns: [/^(?:export\s+)?(?:function|class|interface|type|const|let|var)\s+(\w+)/],
		commentPatterns: [/\/\/.*$/, /\/\*[\s\S]*?\*\//],
		indentationStyle: "spaces",
		indentationSize: 2,
	},
	javascript: {
		language: "javascript",
		maxContextLines: 50,
		importPatterns: [/^import\s+.*?from\s+['"`].*?['"`]/, /^const\s+.*?=\s+require\(/],
		definitionPatterns: [/^(?:export\s+)?(?:function|class|const|let|var)\s+(\w+)/],
		commentPatterns: [/\/\/.*$/, /\/\*[\s\S]*?\*\//],
		indentationStyle: "spaces",
		indentationSize: 2,
	},
	python: {
		language: "python",
		maxContextLines: 50,
		importPatterns: [/^(?:import\s+\w+|from\s+\w+\s+import\s+.*)/],
		definitionPatterns: [/^(?:def|class)\s+(\w+)/],
		commentPatterns: [/#.*$/],
		indentationStyle: "spaces",
		indentationSize: 4,
	},
	java: {
		language: "java",
		maxContextLines: 40,
		importPatterns: [/^import\s+[\w.]+;/],
		definitionPatterns: [/^(?:public|private|protected)?\s*(?:static)?\s*(?:class|interface|enum)\s+(\w+)/],
		commentPatterns: [/\/\/.*$/, /\/\*[\s\S]*?\*\//],
		indentationStyle: "spaces",
		indentationSize: 4,
	},
	csharp: {
		language: "csharp",
		maxContextLines: 40,
		importPatterns: [/^using\s+[\w.]+;/],
		definitionPatterns: [/^(?:public|private|protected|internal)?\s*(?:static)?\s*(?:class|interface|struct|enum)\s+(\w+)/],
		commentPatterns: [/\/\/.*$/, /\/\*[\s\S]*?\*\//],
		indentationStyle: "spaces",
		indentationSize: 4,
	},
	go: {
		language: "go",
		maxContextLines: 40,
		importPatterns: [/^import\s+(?:\(\s*[\s\S]*?\s*\)|"[^"]*")/],
		definitionPatterns: [/^(?:func|type)\s+(\w+)/],
		commentPatterns: [/\/\/.*$/, /\/\*[\s\S]*?\*\//],
		indentationStyle: "tabs",
		indentationSize: 1,
	},
	rust: {
		language: "rust",
		maxContextLines: 40,
		importPatterns: [/^use\s+[\w:]+(?:\s*::\s*\{[^}]*\})?;/],
		definitionPatterns: [/^(?:pub\s+)?(?:fn|struct|enum|trait|impl)\s+(\w+)/],
		commentPatterns: [/\/\/.*$/, /\/\*[\s\S]*?\*\//],
		indentationStyle: "spaces",
		indentationSize: 4,
	},
}

/**
 * Cache entry for completions
 */
export interface CacheEntry {
	key: string
	completions: string[]
	timestamp: number
	hitCount: number
	language: string
	context: string
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
	requestLatency: number
	cacheLatency: number
	contextGatheringTime: number
	templateRenderingTime: number
	apiCallTime: number
	totalTime: number
}

/**
 * Error types for autocomplete
 */
export type AutocompleteErrorType =
	| "api_error"
	| "network_error"
	| "timeout_error"
	| "context_error"
	| "template_error"
	| "cache_error"
	| "unknown_error"

/**
 * Autocomplete error
 */
export interface AutocompleteError {
	type: AutocompleteErrorType
	message: string
	details?: any
	timestamp: number
	context?: CompletionContext
}

/**
 * Event types for autocomplete
 */
export type AutocompleteEventType =
	| "completion_requested"
	| "completion_generated"
	| "completion_accepted"
	| "completion_rejected"
	| "completion_cached"
	| "error_occurred"
	| "status_changed"

/**
 * Autocomplete event
 */
export interface AutocompleteEvent {
	type: AutocompleteEventType
	timestamp: number
	data?: any
	context?: CompletionContext
	performance?: PerformanceMetrics
	error?: AutocompleteError
}
