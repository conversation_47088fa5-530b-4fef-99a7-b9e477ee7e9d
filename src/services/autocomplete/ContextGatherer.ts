import * as vscode from "vscode"
import * as path from "path"

export interface CodeContext {
	precedingLines: string[]
	followingLines: string[]
	imports: string[]
	definitions: string[]
	currentLine: string
	indentation: string
	language: string
	filePath: string
}

export class ContextGatherer {
	private readonly maxContextLines = 50
	private readonly maxImports = 20
	private readonly maxDefinitions = 10

	async gatherContext(
		document: vscode.TextDocument,
		position: vscode.Position,
		includeImports: boolean = true,
		includeDefinitions: boolean = true,
	): Promise<CodeContext> {
		const currentLine = document.lineAt(position.line).text
		const indentation = this.getIndentation(currentLine)

		// Get preceding and following lines
		const precedingLines = this.getPrecedingLines(document, position)
		const followingLines = this.getFollowingLines(document, position)

		// Get imports and definitions
		const imports = includeImports ? await this.getImports(document) : []
		const definitions = includeDefinitions ? await this.getDefinitions(document) : []

		return {
			precedingLines,
			followingLines,
			imports,
			definitions,
			currentLine,
			indentation,
			language: document.languageId,
			filePath: document.uri.fsPath,
		}
	}

	private getPrecedingLines(document: vscode.TextDocument, position: vscode.Position): string[] {
		const lines: string[] = []
		const startLine = Math.max(0, position.line - this.maxContextLines)

		for (let i = startLine; i < position.line; i++) {
			lines.push(document.lineAt(i).text)
		}

		return lines
	}

	private getFollowingLines(document: vscode.TextDocument, position: vscode.Position): string[] {
		const lines: string[] = []
		const endLine = Math.min(document.lineCount - 1, position.line + this.maxContextLines)

		for (let i = position.line + 1; i <= endLine; i++) {
			lines.push(document.lineAt(i).text)
		}

		return lines
	}

	private async getImports(document: vscode.TextDocument): Promise<string[]> {
		const imports: string[] = []
		const text = document.getText()
		const language = document.languageId

		// Different import patterns for different languages
		let importRegex: RegExp

		switch (language) {
			case "typescript":
			case "javascript":
			case "typescriptreact":
			case "javascriptreact":
				importRegex = /^import\s+.*?from\s+['"`].*?['"`]/gm
				break
			case "python":
				importRegex = /^(?:import\s+\w+|from\s+\w+\s+import\s+.*)/gm
				break
			case "java":
				importRegex = /^import\s+[\w.]+;/gm
				break
			case "csharp":
				importRegex = /^using\s+[\w.]+;/gm
				break
			case "go":
				importRegex = /^import\s+(?:\(\s*[\s\S]*?\s*\)|"[^"]*")/gm
				break
			case "rust":
				importRegex = /^use\s+[\w:]+(?:\s*::\s*\{[^}]*\})?;/gm
				break
			default:
				return imports
		}

		let match
		while ((match = importRegex.exec(text)) !== null && imports.length < this.maxImports) {
			imports.push(match[0].trim())
		}

		return imports
	}

	private async getDefinitions(document: vscode.TextDocument): Promise<string[]> {
		const definitions: string[] = []
		const text = document.getText()
		const language = document.languageId

		// Different definition patterns for different languages
		let definitionRegex: RegExp

		switch (language) {
			case "typescript":
			case "javascript":
			case "typescriptreact":
			case "javascriptreact":
				definitionRegex = /^(?:export\s+)?(?:function|class|interface|type|const|let|var)\s+(\w+)/gm
				break
			case "python":
				definitionRegex = /^(?:def|class)\s+(\w+)/gm
				break
			case "java":
				definitionRegex = /^(?:public|private|protected)?\s*(?:static)?\s*(?:class|interface|enum)\s+(\w+)/gm
				break
			case "csharp":
				definitionRegex =
					/^(?:public|private|protected|internal)?\s*(?:static)?\s*(?:class|interface|struct|enum)\s+(\w+)/gm
				break
			case "go":
				definitionRegex = /^(?:func|type)\s+(\w+)/gm
				break
			case "rust":
				definitionRegex = /^(?:pub\s+)?(?:fn|struct|enum|trait|impl)\s+(\w+)/gm
				break
			default:
				return definitions
		}

		let match
		while ((match = definitionRegex.exec(text)) !== null && definitions.length < this.maxDefinitions) {
			definitions.push(match[0].trim())
		}

		return definitions
	}

	private getIndentation(line: string): string {
		const match = line.match(/^(\s*)/)
		return match ? match[1] : ""
	}

	/**
	 * Get related files that might be relevant for context
	 */
	async getRelatedFiles(document: vscode.TextDocument): Promise<string[]> {
		const relatedFiles: string[] = []
		const currentDir = path.dirname(document.uri.fsPath)
		const currentFileName = path.basename(document.uri.fsPath, path.extname(document.uri.fsPath))

		try {
			// Look for files with similar names or in the same directory
			const files = await vscode.workspace.findFiles(
				new vscode.RelativePattern(currentDir, "**/*.{ts,js,tsx,jsx,py,java,cs,go,rs}"),
				null,
				10,
			)

			for (const file of files) {
				const fileName = path.basename(file.fsPath, path.extname(file.fsPath))

				// Skip the current file
				if (file.fsPath === document.uri.fsPath) {
					continue
				}

				// Include files with similar names or common patterns
				if (
					fileName.includes(currentFileName) ||
					currentFileName.includes(fileName) ||
					fileName.endsWith("Types") ||
					fileName.endsWith("Interface") ||
					fileName.endsWith("Config") ||
					fileName === "index"
				) {
					relatedFiles.push(file.fsPath)
				}
			}
		} catch (error) {
			console.error("Error finding related files:", error)
		}

		return relatedFiles.slice(0, 5) // Limit to 5 related files
	}
}
