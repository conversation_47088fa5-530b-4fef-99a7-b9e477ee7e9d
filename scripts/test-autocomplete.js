#!/usr/bin/env node

/**
 * QAX Code Autocomplete 测试脚本
 * 用于验证 autocomplete 功能是否正常工作
 */

const fs = require('fs')
const path = require('path')

class AutocompleteTestRunner {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        }
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString()
        const prefix = {
            'info': '📋',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️'
        }[type] || '📋'
        
        console.log(`${prefix} [${timestamp}] ${message}`)
    }

    async runTest(name, testFn) {
        this.log(`Running test: ${name}`)
        try {
            await testFn()
            this.results.passed++
            this.results.tests.push({ name, status: 'passed' })
            this.log(`Test passed: ${name}`, 'success')
        } catch (error) {
            this.results.failed++
            this.results.tests.push({ name, status: 'failed', error: error.message })
            this.log(`Test failed: ${name} - ${error.message}`, 'error')
        }
    }

    // 测试文件结构
    async testFileStructure() {
        const requiredFiles = [
            'src/services/autocomplete/AutocompleteProvider.ts',
            'src/services/autocomplete/ContextGatherer.ts',
            'src/services/autocomplete/AutocompleteDecorationAnimation.ts',
            'src/services/autocomplete/types.ts',
            'src/services/autocomplete/templating/AutocompleteTemplate.ts',
            'src/services/autocomplete/context/snippetProvider.ts',
            'src/services/autocomplete/utils/createDebouncedFn.ts',
            'src/services/autocomplete/utils/EditDetectionUtils.ts',
            'webview-ui/src/components/settings/AutocompleteSettingsSection.tsx'
        ]

        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Required file missing: ${file}`)
            }
        }
    }

    // 测试 TypeScript 编译
    async testTypeScriptCompilation() {
        const { exec } = require('child_process')
        
        return new Promise((resolve, reject) => {
            exec('npm run compile', (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`Compilation failed: ${error.message}`))
                } else {
                    resolve()
                }
            })
        })
    }

    // 测试 package.json 配置
    async testPackageJsonConfig() {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        
        // 检查配置项
        const config = packageJson.contributes?.configuration
        if (!config || !Array.isArray(config)) {
            throw new Error('Configuration not found or not an array')
        }

        const autocompleteConfig = config.find(c => c.title === 'QAX Code Autocomplete')
        if (!autocompleteConfig) {
            throw new Error('QAX Code Autocomplete configuration not found')
        }

        // 检查必需的配置属性
        const requiredProps = [
            'qax-code.autocomplete.enabled',
            'qax-code.autocomplete.model',
            'qax-code.autocomplete.apiKey',
            'qax-code.autocomplete.baseUrl'
        ]

        for (const prop of requiredProps) {
            if (!autocompleteConfig.properties[prop]) {
                throw new Error(`Required configuration property missing: ${prop}`)
            }
        }

        // 检查命令
        const commands = packageJson.contributes?.commands || []
        const toggleCommand = commands.find(cmd => cmd.command === 'qax-code.toggleAutocomplete')
        if (!toggleCommand) {
            throw new Error('Toggle autocomplete command not found')
        }
    }

    // 测试依赖
    async testDependencies() {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
        
        if (!dependencies['lru-cache']) {
            throw new Error('lru-cache dependency not found')
        }
    }

    // 测试代码语法
    async testCodeSyntax() {
        const { exec } = require('child_process')
        
        return new Promise((resolve, reject) => {
            exec('npx tsc --noEmit', (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`TypeScript syntax errors: ${stderr}`))
                } else {
                    resolve()
                }
            })
        })
    }

    // 生成测试报告
    generateReport() {
        const total = this.results.passed + this.results.failed
        const successRate = total > 0 ? (this.results.passed / total * 100).toFixed(2) : 0

        this.log('\n📊 Test Report', 'info')
        this.log(`Total tests: ${total}`)
        this.log(`Passed: ${this.results.passed}`, 'success')
        this.log(`Failed: ${this.results.failed}`, this.results.failed > 0 ? 'error' : 'info')
        this.log(`Success rate: ${successRate}%`)

        if (this.results.failed > 0) {
            this.log('\n❌ Failed tests:', 'error')
            this.results.tests
                .filter(test => test.status === 'failed')
                .forEach(test => {
                    this.log(`  - ${test.name}: ${test.error}`, 'error')
                })
        }

        return this.results.failed === 0
    }

    async run() {
        this.log('🚀 Starting QAX Code Autocomplete tests...')

        await this.runTest('File Structure', () => this.testFileStructure())
        await this.runTest('Package.json Configuration', () => this.testPackageJsonConfig())
        await this.runTest('Dependencies', () => this.testDependencies())
        await this.runTest('Code Syntax', () => this.testCodeSyntax())
        await this.runTest('TypeScript Compilation', () => this.testTypeScriptCompilation())

        const success = this.generateReport()
        
        if (success) {
            this.log('\n🎉 All tests passed! Autocomplete integration looks good.', 'success')
            this.log('\n📋 Next steps:')
            this.log('1. Run: npm run package')
            this.log('2. Install the generated .vsix file in VS Code')
            this.log('3. Test the autocomplete functionality manually')
            this.log('4. Check the configuration UI in Cline settings')
        } else {
            this.log('\n💥 Some tests failed. Please fix the issues before proceeding.', 'error')
            process.exit(1)
        }
    }
}

// 运行测试
if (require.main === module) {
    const runner = new AutocompleteTestRunner()
    runner.run().catch(error => {
        console.error('❌ Test runner failed:', error)
        process.exit(1)
    })
}

module.exports = AutocompleteTestRunner
