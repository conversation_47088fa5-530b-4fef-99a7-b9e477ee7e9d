# 🚀 QAX Code Autocomplete 快速验证指南

## 立即验证 Autocomplete 是否工作

### 第一步：构建和安装扩展

```bash
# 1. 运行测试脚本（可选）
node scripts/test-autocomplete.js

# 2. 构建扩展
npm run package

# 3. 安装扩展
# 在 VS Code 中：Ctrl+Shift+P -> "Extensions: Install from VSIX" -> 选择生成的 .vsix 文件
```

### 第二步：基础检查（30秒）

1. **检查扩展是否加载**
   - 打开 VS Code
   - 查看活动栏是否有 Cline 图标 ✅
   - 点击 Cline 图标，侧边栏应该打开 ✅

2. **检查配置界面**
   - 点击 Cline 侧边栏的设置按钮（齿轮图标）
   - 查看是否有 "Autocomplete" 标签页 ✅
   - 点击 Autocomplete 标签页，应该看到配置选项 ✅

3. **检查状态栏**
   - 查看 VS Code 底部状态栏
   - 应该看到 "$(sparkle) QAX Complete" 图标 ✅

### 第三步：功能测试（2分钟）

1. **创建测试文件**
   ```bash
   # 复制提供的测试文件
   cp test-files/test-typescript.ts ./test.ts
   ```

2. **测试自动补全**
   - 打开 `test.ts` 文件
   - 找到 "测试点 1" 注释
   - 在空行输入 `this.`
   - 应该看到：
     - QAX 加载动画（打字效果）✅
     - 自动补全建议出现 ✅
     - 状态栏显示成本信息 ✅

3. **测试不同语言**
   ```bash
   # 测试 Python
   cp test-files/test-python.py ./test.py
   ```
   - 打开 `test.py`
   - 在空行输入 `self.`
   - 应该看到 Python 相关的补全建议 ✅

### 第四步：调试工具验证（1分钟）

1. **运行诊断**
   - 按 `Ctrl+Shift+P`
   - 输入 "QAX Autocomplete Diagnostic"
   - 选择 "Run QAX Autocomplete Diagnostic"
   - 查看输出面板的诊断信息 ✅

2. **检查配置**
   - 按 `Ctrl+Shift+P`
   - 输入 "Check QAX Autocomplete Configuration"
   - 运行命令，查看配置是否正确 ✅

## 🔍 问题排查

### 如果自动补全不工作：

1. **检查开发者控制台**
   ```bash
   # 打开开发者控制台
   Ctrl+Shift+I (Windows/Linux) 或 Cmd+Option+I (Mac)
   
   # 查找错误信息
   # 应该看到类似这样的日志：
   # "🚀🔍 Autocomplete state changed to: true"
   # "🚀🛑 Autocomplete for line with prefix"
   ```

2. **检查配置**
   - 确保 Autocomplete 在设置中已启用
   - 确保 API 配置正确
   - 确保实验性功能已启用

3. **检查网络连接**
   - 确保可以访问 `https://aip.b.qianxin-inc.cn/v2`
   - 确保 API Key 有效

### 如果状态栏不显示：

1. **检查扩展激活**
   ```javascript
   // 在开发者控制台中运行
   vscode.extensions.getExtension('your-publisher.cline')?.isActive
   ```

2. **检查命令注册**
   ```javascript
   // 检查命令是否注册
   vscode.commands.getCommands().then(cmds => 
     console.log(cmds.filter(c => c.includes('qax-code')))
   )
   ```

### 如果配置界面不显示：

1. **检查 Webview 错误**
   - 右键点击 Cline 侧边栏
   - 选择 "检查元素"
   - 查看控制台错误

2. **检查组件注册**
   - 确保 `AutocompleteSettingsSection.tsx` 正确导入
   - 确保在 `SettingsView.tsx` 中正确注册

## ✅ 成功标志

如果看到以下所有标志，说明 Autocomplete 集成成功：

- [ ] 扩展正确加载，无错误
- [ ] 配置界面显示 Autocomplete 标签页
- [ ] 状态栏显示 QAX Complete 图标
- [ ] 在代码文件中能触发自动补全
- [ ] 看到 QAX 品牌加载动画
- [ ] 补全建议质量良好
- [ ] 状态栏显示成本信息
- [ ] 调试命令正常工作
- [ ] 配置可以保存和生效

## 🎯 性能基准

**正常性能指标：**
- 首次补全响应时间：< 3秒
- 缓存补全响应时间：< 500ms
- 内存使用增长：< 50MB
- 无明显的 UI 卡顿

## 📞 获取帮助

如果遇到问题：

1. **运行完整诊断**
   ```bash
   # 在 VS Code 中运行
   Ctrl+Shift+P -> "Run QAX Autocomplete Diagnostic"
   ```

2. **保存诊断报告**
   ```bash
   # 在 VS Code 中运行
   Ctrl+Shift+P -> "Save QAX Autocomplete Diagnostic"
   ```

3. **查看详细日志**
   ```bash
   # 查看输出面板
   Ctrl+Shift+U -> 选择 "QAX Autocomplete Debug"
   ```

## 🚀 下一步

验证成功后，您可以：

1. **自定义配置**
   - 尝试不同的模型
   - 调整性能参数
   - 配置自己的 API 密钥

2. **日常使用**
   - 在实际项目中使用
   - 体验不同语言的补全
   - 利用缓存提升效率

3. **反馈和改进**
   - 记录使用体验
   - 报告问题和建议
   - 参与功能改进

恭喜！您已经成功集成了 QAX Code Autocomplete 功能！🎉
