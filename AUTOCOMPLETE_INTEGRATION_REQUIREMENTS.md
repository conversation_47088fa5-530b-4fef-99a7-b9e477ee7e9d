# QAX Code Autocomplete 集成需求文档

## 项目概述

本文档详细描述了将 QAX Code Autocomplete 功能集成到 Cline 扩展中的完整需求和实现方案。

## 核心需求

### 1. 功能集成要求
- ✅ 集成 autocomplete 模块到 Cline 扩展
- ✅ 保持 autocomplete 功能完整性，包括状态栏显示
- ✅ 最小化对现有代码的修改
- ✅ 将所有 "kilo" 引用替换为 "QAX"
- ✅ 移除对外部依赖的引用（kilo、contextProxy、roo-code/types）
- ✅ 在 Cline 配置界面中添加独立的 autocomplete 配置选项

### 2. 配置要求
- ✅ 独立的 autocomplete 配置，与主扩展 API provider 配置分离
- ✅ 支持自定义模型、API base URL、API key
- ✅ 提供性能调优选项（debounce、缓存、最大行数等）

## 实现方案

### 1. 核心模块创建

#### 1.1 AutocompleteProvider (`src/services/autocomplete/AutocompleteProvider.ts`)
- **功能**: 主要的自动补全提供器
- **特性**:
  - 支持实验性功能开关
  - LRU 缓存机制
  - 防抖处理
  - 成本跟踪
  - 状态栏集成
  - 命令注册

#### 1.2 ContextGatherer (`src/services/autocomplete/ContextGatherer.ts`)
- **功能**: 代码上下文收集器
- **特性**:
  - 多语言支持（TypeScript、Python、Java、C#、Go、Rust等）
  - 智能导入和定义提取
  - 相关文件发现
  - 缩进检测

#### 1.3 模板系统 (`src/services/autocomplete/templating/AutocompleteTemplate.ts`)
- **功能**: 提示模板管理
- **特性**:
  - 系统提示生成
  - 上下文模板化
  - 多种完成模式支持

#### 1.4 工具函数
- **createDebouncedFn** (`src/services/autocomplete/utils/createDebouncedFn.ts`): 防抖和节流函数
- **EditDetectionUtils** (`src/services/autocomplete/utils/EditDetectionUtils.ts`): 编辑检测工具
- **snippetProvider** (`src/services/autocomplete/context/snippetProvider.ts`): 代码片段提供器

#### 1.5 动画系统 (`src/services/autocomplete/AutocompleteDecorationAnimation.ts`)
- **功能**: 加载动画管理
- **特性**:
  - QAX 品牌动画
  - 两阶段动画（打字 + 闪烁）
  - 性能优化的延迟显示

### 2. 扩展集成

#### 2.1 主入口修改 (`src/extension.ts`)
```typescript
// 添加导入
import { registerAutocomplete, AutocompleteConfigProvider } from "./services/autocomplete/AutocompleteProvider"
import { DEFAULT_AUTOCOMPLETE_SETTINGS, ExperimentId } from "./services/autocomplete/types"

// 创建配置提供器
const autocompleteConfigProvider: AutocompleteConfigProvider = {
    getAutocompleteConfig() { /* 从 VS Code 配置读取 */ },
    getExperiments() { /* 实验性功能配置 */ }
}

// 注册 autocomplete 功能
registerAutocomplete(context, autocompleteConfigProvider)
```

#### 2.2 配置界面集成

##### 2.2.1 设置视图更新 (`webview-ui/src/components/settings/SettingsView.tsx`)
- 添加 "Autocomplete" 标签页
- 使用 Sparkles 图标
- 集成到现有标签页系统

##### 2.2.2 Autocomplete 设置组件 (`webview-ui/src/components/settings/AutocompleteSettingsSection.tsx`)
- **配置选项**:
  - 启用/禁用开关
  - 模型选择（支持多个 Qwen 和 CodeLlama 模型）
  - API 配置（Base URL、API Key）
  - 性能设置（最大补全数、防抖延迟、最大行数）
- **用户体验**:
  - 实时状态显示
  - 未保存更改提醒
  - 重置功能

#### 2.3 消息处理系统

##### 2.3.1 WebviewMessage 扩展 (`src/shared/WebviewMessage.ts`)
```typescript
type: "autocompleteSettings"
autocompleteSettings?: {
    enabled: boolean
    model: string
    apiKey: string
    baseUrl: string
    maxCompletions: number
    debounceMs: number
    maxLines: number
}
```

##### 2.3.2 Controller 消息处理 (`src/core/controller/index.ts`)
```typescript
case "autocompleteSettings": {
    if (message.autocompleteSettings) {
        await this.updateAutocompleteSettings(message.autocompleteSettings)
    }
    break
}
```

### 3. 配置系统

#### 3.1 VS Code 配置 (`package.json`)
```json
{
    "title": "QAX Code Autocomplete",
    "properties": {
        "qax-code.autocomplete.enabled": { "type": "boolean", "default": true },
        "qax-code.autocomplete.model": { "type": "string", "default": "Qwen2.5-Coder-32B-Instruct" },
        "qax-code.autocomplete.apiKey": { "type": "string", "default": "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b" },
        "qax-code.autocomplete.baseUrl": { "type": "string", "default": "https://aip.b.qianxin-inc.cn/v2" },
        // ... 其他配置项
    }
}
```

#### 3.2 命令注册
- `qax-code.toggleAutocomplete`: 切换自动补全开关

### 4. 依赖管理

#### 4.1 新增依赖
- `lru-cache`: LRU 缓存实现

#### 4.2 构建配置
- esbuild 配置已支持新模块
- 别名解析器支持 autocomplete 路径

## 技术特性

### 1. 性能优化
- **LRU 缓存**: 缓存最近的补全结果，提高响应速度
- **防抖处理**: 避免频繁的 API 调用
- **智能上下文**: 只收集相关的代码上下文
- **增量更新**: 支持流式响应处理

### 2. 用户体验
- **状态栏指示器**: 显示当前状态和成本信息
- **加载动画**: QAX 品牌动画提供视觉反馈
- **智能缓存**: 基于上下文的智能缓存策略
- **多语言支持**: 支持主流编程语言

### 3. 可配置性
- **模型选择**: 支持多个 AI 模型
- **性能调优**: 可调节防抖延迟、缓存大小等
- **实验性功能**: 支持功能开关控制

## 文件结构

```
src/services/autocomplete/
├── AutocompleteProvider.ts          # 主提供器
├── ContextGatherer.ts               # 上下文收集
├── AutocompleteDecorationAnimation.ts # 动画管理
├── types.ts                         # 类型定义
├── templating/
│   └── AutocompleteTemplate.ts      # 模板系统
├── context/
│   └── snippetProvider.ts          # 代码片段
└── utils/
    ├── createDebouncedFn.ts         # 防抖函数
    └── EditDetectionUtils.ts       # 编辑检测

webview-ui/src/components/settings/
└── AutocompleteSettingsSection.tsx  # 设置界面
```

## 验证清单

### ✅ 功能验证
- [x] 扩展成功编译
- [x] 配置界面正确显示
- [x] 消息传递系统工作正常
- [x] VS Code 配置正确注册
- [x] 命令正确注册

### ✅ 代码质量
- [x] 通过 ESLint 检查
- [x] 通过 TypeScript 类型检查
- [x] 遵循项目代码规范
- [x] 适当的错误处理

### ✅ 集成验证
- [x] 不影响现有功能
- [x] 配置独立性
- [x] 正确的依赖管理
- [x] 构建脚本兼容

## 后续建议

1. **测试**: 建议添加单元测试和集成测试
2. **文档**: 为用户提供使用指南
3. **监控**: 添加性能监控和错误报告
4. **优化**: 根据用户反馈进行性能优化

## 总结

本次集成成功将 QAX Code Autocomplete 功能完整集成到 Cline 扩展中，实现了：

1. **完整功能保留**: 所有 autocomplete 功能都得到保留
2. **独立配置系统**: 与主扩展配置完全分离
3. **最小侵入性**: 对现有代码的修改最小化
4. **良好的用户体验**: 提供直观的配置界面和状态反馈
5. **高性能**: 通过缓存和防抖优化性能
6. **可扩展性**: 支持未来功能扩展

集成后的扩展为用户提供了强大的 AI 驱动代码自动补全功能，同时保持了 Cline 扩展的原有特性和稳定性。
